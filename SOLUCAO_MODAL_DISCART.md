# Solução para o Problema do Modal Discart

## Problema Identificado

O modal `openModalDiscart` estava abrindo corretamente quando o usuário tentava navegar para outro card de NCM com alterações não salvas, mas o próximo card era aberto **antes** do usuário escolher uma opção no modal (Descartar, Salvar ou Cancelar).

### Fluxo Problemático Original:
1. Usuário preenche informações em uma NCM
2. Usuário clica em outro card de NCM
3. Modal abre perguntando sobre as alterações
4. **PROBLEMA**: O próximo card já era aberto imediatamente, sem aguardar a decisão do usuário

## Solução Implementada

### 1. Sistema de Ação Pendente
Adicionado um sistema que armazena a ação que o usuário quer executar e só a executa após a decisão no modal.

**Arquivo**: `WfAgrupamentoNcm.vue`
- Adicionada propriedade `pendingAction` no data()
- Modificado método `expandCollapse()` para armazenar a ação pendente
- Modificado método `closeCollapse()` para processar a ação pendente

### 2. Novo Comportamento do Modal
**Arquivo**: `modalDiscart.vue`
- Adicionado método `cancelar()` que emite evento `cancelarAcao`
- Botão "Cancelar" agora chama o método `cancelar()`

### 3. Tratamento das Ações do Modal

#### Quando usuário clica em "Descartar":
- Executa a ação pendente (abre o próximo card)
- Limpa as alterações da NCM anterior

#### Quando usuário clica em "Salvar":
- Salva as alterações da NCM atual
- Executa a ação pendente (abre o próximo card)

#### Quando usuário clica em "Cancelar":
- **NOVO**: Limpa a ação pendente
- Fecha o modal
- Permanece no card atual (NCM que estava sendo editada)

## Fluxo Corrigido

### Cenário: Usuário com alterações não salvas clica em outro card

1. **Usuário preenche informações** na NCM atual
2. **Usuário clica em outro card** de NCM
3. **Sistema detecta alterações** não salvas
4. **Sistema armazena** qual NCM o usuário quer abrir (`pendingAction`)
5. **Modal abre** perguntando sobre as alterações
6. **Sistema aguarda** decisão do usuário
7. **Usuário escolhe uma opção**:

   **Se escolher "Descartar":**
   - Descarta alterações da NCM atual
   - Executa ação pendente (abre a NCM clicada)
   
   **Se escolher "Salvar":**
   - Salva alterações da NCM atual
   - Executa ação pendente (abre a NCM clicada)
   
   **Se escolher "Cancelar":**
   - Limpa ação pendente
   - Fecha modal
   - Permanece na NCM atual

## Arquivos Modificados

### 1. `WfAgrupamentoNcm.vue`
- **Linha 135**: Adicionada propriedade `pendingAction`
- **Linhas 431-439**: Modificado `expandCollapse()` para armazenar ação pendente
- **Linhas 473-477**: Adicionado método `cancelarAcao()`
- **Linhas 482-503**: Modificado `closeCollapse()` para processar ação pendente
- **Linha 92**: Adicionado listener `@cancelarAcao="cancelarAcao"`

### 2. `modalDiscart.vue`
- **Linha 19**: Adicionado `@click="cancelar"` no botão Cancelar
- **Linhas 62-65**: Adicionado método `cancelar()`

## Benefícios da Solução

1. **Aguarda decisão do usuário**: O próximo card só abre após escolha no modal
2. **Preserva funcionalidade existente**: Botões "Descartar" e "Salvar" continuam funcionando
3. **Adiciona funcionalidade "Cancelar"**: Usuário pode cancelar a navegação e continuar editando
4. **Não quebra outras funcionalidades**: Seleção em massa e outros fluxos permanecem inalterados
5. **Código limpo**: Solução implementada de forma organizada e documentada

## Teste da Solução

Para testar se a solução está funcionando:

1. Abra uma NCM e faça alterações nos campos
2. **NÃO** salve as alterações
3. Clique em outro card de NCM
4. Verifique se o modal abre
5. **IMPORTANTE**: Verifique se o próximo card **NÃO** abre automaticamente
6. Teste cada opção do modal:
   - **Cancelar**: Deve fechar modal e permanecer na NCM atual
   - **Descartar**: Deve abrir próximo card sem salvar
   - **Salvar**: Deve salvar e abrir próximo card
